from django.urls import path

from .views import *

urlpatterns = [
    # 获取月子中心列表
    path('maternity-center/list/', MaternityCenterListView.as_view(), name='maternity-center-list'),
    # 校验产妇和月子中心关系
    path('relation/check/<str:cid>/', MaternityCheckView.as_view(), name='maternity-check'),
    # 首页总览
    path('home/overview/<str:aid>/', HomeOverviewView.as_view(), name='home-overview'),
    # 会员详情
    path('member/detail/<str:aid>/', MemberDetailView.as_view(), name='member-detail'),
    # 产检提醒
    path('prenatal/check/<str:aid>/', PrenatalCheckView.as_view(), name='prenatal-check'),
    
    # 活动中心
    path('activity/list/<str:aid>/', ActivityListView.as_view(), name='activity-list'),
    
    # 产妇健康概览
    path('health/overview/<str:aid>/', HealthOverviewView.as_view(), name='health-overview'),
    
    # 产妇膳食
    path('diet/list/<str:aid>/', DietRecordView.as_view(), name='diet-record-list'),
    # 产妇康复护理记录
    path('rehabilitation/list/<str:aid>/', RehabilitationRecordView.as_view(), name='rehabilitation-record-list'),
    # 产妇每日生理护理记录
    path('physical-care/list/<str:aid>/', PhysicalCareRecordView.as_view(), name='physical-care-record-list'),
    # 产妇每日必填记录
    path('daily-required/list/<str:aid>/', DailyRequiredRecordView.as_view(), name='daily-required-record-list'),
    
    
    # 婴儿健康记录
    path('baby/list/<str:aid>/',BabyListdView.as_view(), name='wx-baby-list'),
    # 婴儿喂养记录
    path('baby/feeding/list/<str:nid>/',BabyFeedingRecordView.as_view(), name='wx-baby-feeding-record-list'),
    # 婴儿每日护理记录
    path('baby/daily-care/list/<str:nid>/',BabyDailyCareRecordView.as_view(), name='wx-baby-daily-care-record-list'),
    # 每日体格检查记录
    path('baby/physical-examination/list/<str:nid>/',BabyPhysicalExaminationRecordView.as_view(), name='wx-baby-physical-examination-record-list'),
    
    
    # 意见反馈列表
    path('feedback/list/<str:aid>/',FeedbackListView.as_view(), name='wx-feedback-list'),
    # 意见反馈详情
    path('feedback/detail/<str:rid>/',FeedbackDetailView.as_view(), name='wx-feedback-detail'),
    # 创建意见反馈
    path('feedback/create/<str:aid>/',FeedbackCreateView.as_view(), name='wx-feedback-create'),
    # 更新意见反馈
    path('feedback/update/<str:rid>/',FeedbackUpdateView.as_view(), name='wx-feedback-update'),
    # 删除意见反馈
    path('feedback/delete/<str:rid>/',FeedbackDeleteView.as_view(), name='wx-feedback-delete'),
    
    # 消息列表
    path('message/list/<str:aid>/', MessageListView.as_view(), name='wx-message-list'),
    # 消息详情
    path('message/detail/<str:rid>/', MessageDetailView.as_view(), name='wx-message-detail'),
    # 消息轮询
    path('message/poll/', MessagePollView.as_view(), name='wx-message-poll'),
        
    # 获取小程序预约参观列表
    path('visit/list/', WechatAppVisitListView.as_view(), name='visitor-appointment-list'),
    # 小程序预约参观详情
    path('visit/detail/<str:vid>/', WechatAppVisitDetailView.as_view(), name='visitor-appointment-detail'),
    # 创建小程序预约参观
    path('visit/create/', WechatAppVisitCreateView.as_view(), name='visitor-appointment-create'),
    # 取消小程序预约参观
    path('visit/cancel/<str:vid>/', WechatAppVisitCancelView.as_view(), name='visitor-appointment-cancel'),
    
    # ------------------------------ 未付费用户 ------------------------------
    # 获取产检提醒信息
    path('unpaid/prenatal/info/', UnpaidPrenatalInfoView.as_view(), name='unpaid-prenatal-info'),
    # 创建未付费用户产妇信息
    path('unpaid/prenatal/create/', UnpaidPrenatalCreateView.as_view(), name='unpaid-prenatal-create'),
    # 更新未付费用户产妇信息
    path('unpaid/prenatal/update/', UnpaidPrenatalUpdateView.as_view(), name='unpaid-prenatal-update'),
    
    # 消息列表
    path('unpaid/message/list/', UnpaidMessageListView.as_view(), name='wx-unpaid-message-list'),
    # 消息详情
    path('unpaid/message/detail/<str:rid>/', UnpaidMessageDetailView.as_view(), name='wx-unpaid-message-detail'),
    # 消息轮询
    path('unpaid/message/poll/', UnpaidMessagePollView.as_view(), name='wx-unpaid-message-poll'),
    
    
    # 获取轮播图列表接口
    path('carousel/list/<str:cid>/', MaternityCenterCarouselListView.as_view(), name='carousel-list'),
    # 获取月子中心品牌介绍接口
    path('brand/introduction/<str:cid>/', MaternityCenterBrandIntroductionListView.as_view(), name='brand-introduction-list'),
    # 获取月子中心联系我们接口
    path('contact/us/<str:cid>/', MaternityCenterContactUsListView.as_view(), name='contact-us-list'),
    
    
    
    # 健康知识
    path('health/knowledge/list/<str:cid>/', HealthKnowledgeListView.as_view(), name='health-knowledge-list'),
]